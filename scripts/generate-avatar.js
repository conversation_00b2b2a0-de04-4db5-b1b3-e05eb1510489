const fs = require('fs');
const path = require('path');

// Generate a simple SVG avatar
const avatarSvg = `
<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
  <circle cx="50" cy="50" r="50" fill="#FCD5E3"/>
  <circle cx="50" cy="35" r="15" fill="#eb1966"/>
  <path d="M 25 75 Q 50 60 75 75" stroke="#eb1966" stroke-width="8" fill="none" stroke-linecap="round"/>
</svg>`;

const outputPath = path.join(__dirname, '..', 'public', 'avatars', 'default.svg');
fs.writeFileSync(outputPath, avatarSvg);
console.log('Generated default avatar: default.svg');
