
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FCD5E3;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="400" fill="url(#grad)"/>
  <rect x="20" y="20" width="360" height="360" fill="none" stroke="white" stroke-width="2" opacity="0.3"/>
  
  <!-- Icon area -->
  <circle cx="200" cy="150" r="40" fill="white" opacity="0.9"/>
  <text x="200" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2563eb">🎨</text>
  
  <!-- Title -->
  <foreignObject x="40" y="220" width="320" height="120">
    <div xmlns="http://www.w3.org/1999/xhtml" style="
      font-family: 'Arial', sans-serif;
      font-size: 18px;
      font-weight: bold;
      color: white;
      text-align: center;
      line-height: 1.3;
      padding: 10px;
      text-shadow: 0 1px 3px rgba(0,0,0,0.3);
    ">
      Content Design
101
    </div>
  </foreignObject>
  
  <!-- Bottom decoration -->
  <rect x="0" y="350" width="400" height="50" fill="white" opacity="0.1"/>
  <text x="200" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white" opacity="0.8">ARTISH DIGITAL</text>
</svg>