'use client';

import { useState } from 'react';
import IntakeToggle from './intake-toggle';
import ResultsDropdown from './results-dropdown';

type IntakeMode = 'building' | 'executing';

interface SmartIntakePromptProps {
  mode: IntakeMode;
  onModeChange: (mode: IntakeMode) => void;
}

export default function SmartIntakePrompt({ mode, onModeChange }: SmartIntakePromptProps) {
  const [input, setInput] = useState('');
  const [secondaryInput, setSecondaryInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [showResults, setShowResults] = useState(false);
  const [step, setStep] = useState<'initial' | 'secondary' | 'confirmation'>('initial');
  const [parsedResult, setParsedResult] = useState<any>(null);
  const [selectedFreelancerId, setSelectedFreelancerId] = useState<number | null>(null);
  const [selectedGigId, setSelectedGigId] = useState<number | null>(null);
  const [editingRequirements, setEditingRequirements] = useState(false);
  const [editedRequirements, setEditedRequirements] = useState<any>(null);
  const [showCreateProposal, setShowCreateProposal] = useState(false);

  const handleSubmit = async (confirmed?: boolean, editedData?: any) => {
    setLoading(true);
    setShowResults(true);

    const endpoint =
      mode === 'building' ? '/api/ai-intake/client' : '/api/ai-intake/freelancer';

    const body = mode === 'building'
      ? {
          prompt: input,
          budget: step === 'secondary' ? secondaryInput : undefined,
          step: step,
          selectedFreelancerId: selectedFreelancerId,
          editedRequirements: editedData,
          confirmed: confirmed
        }
      : {
          intent: input,
          preferences: step === 'secondary' ? secondaryInput : undefined,
          step: step,
          selectedGigId: selectedGigId,
          createProposal: showCreateProposal
        };

    const res = await fetch(endpoint, {
      method: 'POST',
      body: JSON.stringify(body),
      headers: { 'Content-Type': 'application/json' },
    });

    const data = await res.json();
    const resultData = data.result || data.error;
    setResult(resultData);

    // Try to parse JSON result for structured data
    try {
      const parsed = JSON.parse(resultData);
      setParsedResult(parsed);

      // Handle different step transitions
      if (parsed.step === 'budget_request') {
        setStep('secondary');
      } else if (parsed.step === 'requirements_confirmation') {
        setStep('confirmation');
      } else if (parsed.step === 'gig_created') {
        // Create the gig in the database
        if (parsed.gigData) {
          await createGig(parsed.gigData);
        }
      }
    } catch (e) {
      // If not JSON, treat as plain text
      setParsedResult(null);
    }

    setLoading(false);
  };

  const createGig = async (gigData: any) => {
    try {
      const res = await fetch('/api/gigs/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ gigData })
      });

      if (res.ok) {
        console.log('Gig created successfully');
      }
    } catch (error) {
      console.error('Failed to create gig:', error);
    }
  };

  const handleFreelancerSelect = (freelancerId: number) => {
    setSelectedFreelancerId(freelancerId);
    // Trigger requirements generation for this specific freelancer
    handleSubmit();
  };

  const handleConfirmRequirements = () => {
    handleSubmit(true);
  };

  const handleEditRequirements = () => {
    setEditingRequirements(true);
    setEditedRequirements(parsedResult?.projectRequirements);
  };

  const handleSaveEdits = () => {
    setEditingRequirements(false);
    handleSubmit(true, editedRequirements);
  };

  const handleGigSelect = (gigId: number) => {
    setSelectedGigId(gigId);
    // Trigger auto-generation for this specific gig
    handleSubmit();
  };

  const handleCreateProposal = () => {
    setShowCreateProposal(true);
    // Trigger commissioner search and proposal generation
    handleSubmit();
  };

  const handleApplyForGig = () => {
    // This would trigger the actual application submission
    // In a real implementation, this would call the apply.tsx submit function
    console.log('Applying for gig with auto-generated data');
  };

  const handleShowOtherGigs = () => {
    // Reset to show other gigs
    setSelectedGigId(null);
    setShowCreateProposal(false);
    // Go back to opportunities list
    handleSubmit();
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Prompt Box */}
      <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg p-4 border border-white/30">
        {/* Heading */}
        <div className="text-center mb-4">
          <h1 className="text-4xl font-bold text-gray-900 mb-1" style={{ fontFamily: 'Plus Jakarta Sans' }}>
            For builders and creators.
          </h1>
          <p className="text-sm font-light text-gray-600">
            Start something bold. Find work worth doing.
          </p>
        </div>
        {/* Centered Toggle */}
        <div className="flex justify-center mb-4">
          <IntakeToggle mode={mode} onChange={onModeChange} />
        </div>

        {/* Main Input */}
        <div className="space-y-4">
          <div className="relative">
            <textarea
              className="w-full p-3 pr-20 border-2 border-black rounded-xl text-sm placeholder-gray-500 focus:border-black focus:outline-none resize-none transition-all bg-gradient-to-r from-white to-gray-50"
              rows={2}
              placeholder={
                mode === 'building'
                  ? 'What are you trying to build?'
                  : 'What kind of work are you looking for?'
              }
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  if (input.trim() && !loading) {
                    handleSubmit();
                  }
                }
              }}
              disabled={step === 'secondary'}
            />
            {/* Embedded Submit Button */}
            <button
              className="absolute bottom-2 right-2 bg-black text-white px-3 py-1.5 rounded-lg text-xs font-medium hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => handleSubmit()}
              disabled={loading || !input || (step === 'secondary' && mode === 'building' && !secondaryInput)}
            >
              {loading ? '...' : step === 'secondary' ? 'Find' : 'Start'}
            </button>
          </div>

          {/* Secondary Input - Budget only (for commissioners) */}
          {step === 'secondary' && parsedResult && mode === 'building' && (
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-xl">
                <p className="text-sm text-gray-700 mb-3">{parsedResult.message}</p>

                {/* Show categories/tools for selection */}
                {parsedResult.categories && parsedResult.categories.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Relevant Categories:</h4>
                    <div className="flex flex-wrap gap-2">
                      {parsedResult.categories.map((category: any, index: number) => (
                        <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                          {typeof category === 'string' ? category : category.name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {parsedResult.tools && parsedResult.tools.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Suggested Tools:</h4>
                    <div className="flex flex-wrap gap-2">
                      {parsedResult.tools.map((tool: any, index: number) => (
                        <span key={index} className="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                          {typeof tool === 'string' ? tool : tool.name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="relative">
                <textarea
                  className="w-full p-3 pr-20 border-2 border-black rounded-xl text-sm placeholder-gray-500 focus:border-black focus:outline-none resize-none transition-all bg-gradient-to-r from-white to-gray-50"
                  rows={2}
                  placeholder={
                    mode === 'building'
                      ? 'What\'s your budget range? (e.g., $5,000 - $10,000)'
                      : 'Which of these categories/tools seem interesting to you?'
                  }
                  value={secondaryInput}
                  onChange={(e) => setSecondaryInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      if (secondaryInput.trim() && !loading) {
                        handleSubmit();
                      }
                    }
                  }}
                />
                <button
                  className="absolute bottom-2 right-2 bg-black text-white px-3 py-1.5 rounded-lg text-xs font-medium hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={() => handleSubmit()}
                  disabled={loading || !secondaryInput}
                >
                  {loading ? '...' : 'Find'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Results Section */}
      {loading && showResults && (
        <div className="mt-4 bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg p-4 border border-white/30">
          <div className="flex items-center justify-center py-6">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black"></div>
            <span className="ml-2 text-gray-600 text-sm">Finding matches...</span>
          </div>
        </div>
      )}

      {/* Results Dropdown */}
      <ResultsDropdown
        results={result || ''}
        isVisible={showResults && !loading && !!result}
        mode={mode}
        onFreelancerSelect={handleFreelancerSelect}
        onConfirmRequirements={handleConfirmRequirements}
        onEditRequirements={handleEditRequirements}
        onSaveEdits={handleSaveEdits}
        onGigSelect={handleGigSelect}
        onCreateProposal={handleCreateProposal}
        onApplyForGig={handleApplyForGig}
        onShowOtherGigs={handleShowOtherGigs}
      />
    </div>
  );
}