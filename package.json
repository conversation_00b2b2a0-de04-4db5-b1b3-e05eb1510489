{"name": "artish-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@continuum-ai/avatars": "^1.0.3", "@headlessui/react": "^2.2.4", "@radix-ui/react-popover": "^1.1.14", "@tailwindcss/postcss": "^4.1.7", "chart.js": "^4.5.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "firebase": "^12.0.0", "framer-motion": "^12.16.0", "libsodium-wrappers": "^0.7.15", "lucide-react": "^0.515.0", "next": "^15.3.5", "next-auth": "^4.24.11", "openai": "^5.10.2", "react": "19.0.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.7.0", "react-dom": "19.0.0", "react-tailwindcss-datepicker": "^2.0.0", "recharts": "^3.1.0", "slugify": "^1.6.6"}, "devDependencies": {"@types/chart.js": "^2.9.41", "@types/libsodium-wrappers": "^0.7.14", "@types/node": "^20", "@types/react": "^19", "@types/react-day-picker": "^5.2.1", "@types/react-dom": "^19", "@types/recharts": "^1.8.29", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "^5.8.3"}}