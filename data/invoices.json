[{"invoiceNumber": "MGL100000-M1", "freelancerId": 31, "projectId": 301, "commissionerId": 32, "projectTitle": "Lagos Parks Services website re-design", "milestoneDescription": "Milestone 1 for Lagos Parks Services website re-design", "milestoneNumber": 1, "issueDate": "2025-06-01", "dueDate": "2025-06-10", "totalAmount": 1748.0, "status": "paid", "paidDate": "2025-06-08", "paymentDetails": {"paymentId": "pay_1748_001", "paymentMethod": "stripe", "platformFee": 87.4, "freelancerAmount": 1660.6, "currency": "USD", "processedAt": "2025-06-08T14:30:00Z"}, "milestones": [{"description": "Milestone 1 for Lagos Parks Services website re-design", "rate": 1748.0}], "isManualInvoice": true, "parentInvoiceNumber": "MGL100000"}, {"invoiceNumber": "MGL100000-M2", "freelancerId": 31, "projectId": 301, "commissionerId": 32, "projectTitle": "Lagos Parks Services website re-design", "milestoneDescription": "Milestone 2 for Lagos Parks Services website re-design", "milestoneNumber": 2, "issueDate": "2025-06-05", "dueDate": "2025-06-14", "totalAmount": 1748.0, "status": "sent", "milestones": [{"description": "Milestone 2 for Lagos Parks Services website re-design", "rate": 1748.0}], "isManualInvoice": true, "parentInvoiceNumber": "MGL100000"}, {"invoiceNumber": "MGL100000-M3", "freelancerId": 31, "projectId": 301, "commissionerId": 32, "projectTitle": "Lagos Parks Services website re-design", "milestoneDescription": "Milestone 3 for Lagos Parks Services website re-design", "milestoneNumber": 3, "issueDate": "2025-06-08", "dueDate": "2025-06-17", "totalAmount": 1748.0, "status": "draft", "milestones": [{"description": "Milestone 3 for Lagos Parks Services website re-design", "rate": 1748.0}], "isManualInvoice": true, "parentInvoiceNumber": "MGL100000"}, {"invoiceNumber": "CUSTOM-001-M1", "freelancerId": 31, "projectId": null, "commissionerId": 33, "projectTitle": "Custom Brand Consultation", "milestoneDescription": "Brand strategy consultation", "milestoneNumber": 1, "issueDate": "2025-07-15", "dueDate": "2025-07-25", "totalAmount": 1500.0, "status": "paid", "milestones": [{"description": "Brand strategy consultation", "rate": 1500.0}], "isCustomProject": true, "isManualInvoice": true, "parentInvoiceNumber": "CUSTOM-001"}, {"invoiceNumber": "CUSTOM-001-M2", "freelancerId": 31, "projectId": null, "commissionerId": 33, "projectTitle": "Custom Brand Consultation", "milestoneDescription": "Brand guidelines document", "milestoneNumber": 2, "issueDate": "2025-07-18", "dueDate": "2025-07-28", "totalAmount": 1000.0, "status": "sent", "milestones": [{"description": "Brand guidelines document", "rate": 1000.0}], "isCustomProject": true, "isManualInvoice": true, "parentInvoiceNumber": "CUSTOM-001"}, {"invoiceNumber": "MGL100002-M1", "freelancerId": 31, "projectId": 302, "commissionerId": 37, "projectTitle": "Media channel branding", "milestoneDescription": "Milestone 1 for Media channel branding", "milestoneNumber": 1, "issueDate": "2025-04-18", "dueDate": "2025-04-25", "totalAmount": 1373.33, "status": "paid", "milestones": [{"description": "Milestone 1 for Media channel branding", "rate": 1373.33}], "parentInvoiceNumber": "MGL100002"}, {"invoiceNumber": "MGL100002-M2", "freelancerId": 31, "projectId": 302, "commissionerId": 37, "projectTitle": "Media channel branding", "milestoneDescription": "Milestone 2 for Media channel branding", "milestoneNumber": 2, "issueDate": "2025-04-22", "dueDate": "2025-04-29", "totalAmount": 1373.33, "status": "sent", "milestones": [{"description": "Milestone 2 for Media channel branding", "rate": 1373.33}], "parentInvoiceNumber": "MGL100002"}, {"invoiceNumber": "MGL100002-M3", "freelancerId": 31, "projectId": 302, "commissionerId": 37, "projectTitle": "Media channel branding", "milestoneDescription": "Milestone 3 for Media channel branding", "milestoneNumber": 3, "issueDate": "2025-04-25", "dueDate": "2025-05-02", "totalAmount": 1373.33, "status": "draft", "milestones": [{"description": "Milestone 3 for Media channel branding", "rate": 1373.33}], "parentInvoiceNumber": "MGL100002"}, {"invoiceNumber": "MGL100003-M1", "freelancerId": 31, "projectId": 302, "commissionerId": 36, "projectTitle": "Podcast series logo design", "milestoneDescription": "Milestone 1 for Podcast series logo design", "milestoneNumber": 1, "issueDate": "2025-03-10", "dueDate": "2025-03-20", "totalAmount": 793.33, "status": "paid", "milestones": [{"description": "Milestone 1 for Podcast series logo design", "rate": 793.33}], "parentInvoiceNumber": "MGL100003"}, {"invoiceNumber": "MGL100003-M2", "freelancerId": 31, "projectId": 302, "commissionerId": 36, "projectTitle": "Podcast series logo design", "milestoneDescription": "Milestone 2 for Podcast series logo design", "milestoneNumber": 2, "issueDate": "2025-03-15", "dueDate": "2025-03-25", "totalAmount": 793.33, "status": "paid", "milestones": [{"description": "Milestone 2 for Podcast series logo design", "rate": 793.33}], "parentInvoiceNumber": "MGL100003"}, {"invoiceNumber": "MGL100003-M3", "freelancerId": 31, "projectId": 302, "commissionerId": 36, "projectTitle": "Podcast series logo design", "milestoneDescription": "Milestone 3 for Podcast series logo design", "milestoneNumber": 3, "issueDate": "2025-03-18", "dueDate": "2025-03-28", "totalAmount": 793.33, "status": "sent", "milestones": [{"description": "Milestone 3 for Podcast series logo design", "rate": 793.33}], "parentInvoiceNumber": "MGL100003"}, {"invoiceNumber": "MGL100004-M1", "freelancerId": 31, "projectId": 303, "commissionerId": 35, "projectTitle": "Event Production Branding", "milestoneDescription": "Milestone 1 for Event Production Branding", "milestoneNumber": 1, "issueDate": "2025-06-05", "dueDate": "2025-06-14", "totalAmount": 2150.0, "status": "paid", "milestones": [{"description": "Milestone 1 for Event Production Branding", "rate": 2150.0}], "parentInvoiceNumber": "MGL100004"}, {"invoiceNumber": "MGL100004-M2", "freelancerId": 31, "projectId": 303, "commissionerId": 35, "projectTitle": "Event Production Branding", "milestoneDescription": "Milestone 2 for Event Production Branding", "milestoneNumber": 2, "issueDate": "2025-06-10", "dueDate": "2025-06-19", "totalAmount": 2150.0, "status": "sent", "milestones": [{"description": "Milestone 2 for Event Production Branding", "rate": 2150.0}], "parentInvoiceNumber": "MGL100004"}, {"invoiceNumber": "MGL100004-M3", "freelancerId": 31, "projectId": 303, "commissionerId": 35, "projectTitle": "Event Production Branding", "milestoneDescription": "Milestone 3 for Event Production Branding", "milestoneNumber": 3, "issueDate": "2025-06-12", "dueDate": "2025-06-21", "totalAmount": 2150.0, "status": "draft", "milestones": [{"description": "Milestone 3 for Event Production Branding", "rate": 2150.0}], "parentInvoiceNumber": "MGL100004"}, {"invoiceNumber": "MGL100005-M1", "freelancerId": 31, "projectId": 299, "commissionerId": 34, "projectTitle": "Corlax iOS App Launch Deck", "milestoneDescription": "Milestone 1 for Corlax iOS App Launch Deck", "milestoneNumber": 1, "issueDate": "2025-05-01", "dueDate": "2025-05-09", "totalAmount": 960.0, "status": "paid", "milestones": [{"description": "Milestone 1 for Corlax iOS App Launch Deck", "rate": 960.0}], "parentInvoiceNumber": "MGL100005"}, {"invoiceNumber": "MGL100005-M2", "freelancerId": 31, "projectId": 299, "commissionerId": 34, "projectTitle": "Corlax iOS App Launch Deck", "milestoneDescription": "Milestone 2 for Corlax iOS App Launch Deck", "milestoneNumber": 2, "issueDate": "2025-05-05", "dueDate": "2025-05-13", "totalAmount": 960.0, "status": "paid", "milestones": [{"description": "Milestone 2 for Corlax iOS App Launch Deck", "rate": 960.0}], "parentInvoiceNumber": "MGL100005"}, {"invoiceNumber": "MGL100005-M3", "freelancerId": 31, "projectId": 299, "commissionerId": 34, "projectTitle": "Corlax iOS App Launch Deck", "milestoneDescription": "Milestone 3 for Corlax iOS App Launch Deck", "milestoneNumber": 3, "issueDate": "2025-05-08", "dueDate": "2025-05-16", "totalAmount": 960.0, "status": "paid", "milestones": [{"description": "Milestone 3 for Corlax iOS App Launch Deck", "rate": 960.0}], "parentInvoiceNumber": "MGL100005"}, {"invoiceNumber": "MGL100006-M1", "freelancerId": 31, "projectId": 300, "commissionerId": 36, "projectTitle": "Micro-content for Launch Campaign", "milestoneDescription": "Milestone 1 for Micro-content for Launch Campaign", "milestoneNumber": 1, "issueDate": "2025-04-08", "dueDate": "2025-04-20", "totalAmount": 458.33, "status": "paid", "milestones": [{"description": "Milestone 1 for Micro-content for Launch Campaign", "rate": 458.33}], "parentInvoiceNumber": "MGL100006"}, {"invoiceNumber": "MGL100006-M2", "freelancerId": 31, "projectId": 300, "commissionerId": 36, "projectTitle": "Micro-content for Launch Campaign", "milestoneDescription": "Milestone 2 for Micro-content for Launch Campaign", "milestoneNumber": 2, "issueDate": "2025-04-12", "dueDate": "2025-04-24", "totalAmount": 458.33, "status": "sent", "milestones": [{"description": "Milestone 2 for Micro-content for Launch Campaign", "rate": 458.33}], "parentInvoiceNumber": "MGL100006"}, {"invoiceNumber": "MGL100006-M3", "freelancerId": 31, "projectId": 300, "commissionerId": 36, "projectTitle": "Micro-content for Launch Campaign", "milestoneDescription": "Milestone 3 for Micro-content for Launch Campaign", "milestoneNumber": 3, "issueDate": "2025-04-15", "dueDate": "2025-04-27", "totalAmount": 458.33, "status": "draft", "milestones": [{"description": "Milestone 3 for Micro-content for Launch Campaign", "rate": 458.33}], "parentInvoiceNumber": "MGL100006"}, {"invoiceNumber": "MGL100007-M1", "freelancerId": 31, "projectId": 313, "commissionerId": 32, "projectTitle": "Social Video Series – 6 Deliverables", "milestoneDescription": "Milestone 1 for Social Video Series – 6 Deliverables", "milestoneNumber": 1, "issueDate": "2025-03-22", "dueDate": "2025-03-30", "totalAmount": 2400.0, "status": "paid", "milestones": [{"description": "Milestone 1 for Social Video Series – 6 Deliverables", "rate": 2400.0}], "parentInvoiceNumber": "MGL100007"}, {"invoiceNumber": "MGL100007-M2", "freelancerId": 31, "projectId": 313, "commissionerId": 32, "projectTitle": "Social Video Series – 6 Deliverables", "milestoneDescription": "Milestone 2 for Social Video Series – 6 Deliverables", "milestoneNumber": 2, "issueDate": "2025-03-26", "dueDate": "2025-04-03", "totalAmount": 2400.0, "status": "paid", "milestones": [{"description": "Milestone 2 for Social Video Series – 6 Deliverables", "rate": 2400.0}], "parentInvoiceNumber": "MGL100007"}, {"invoiceNumber": "MGL100007-M3", "freelancerId": 31, "projectId": 313, "commissionerId": 32, "projectTitle": "Social Video Series – 6 Deliverables", "milestoneDescription": "Milestone 3 for Social Video Series – 6 Deliverables", "milestoneNumber": 3, "issueDate": "2025-03-29", "dueDate": "2025-04-06", "totalAmount": 2400.0, "status": "sent", "milestones": [{"description": "Milestone 3 for Social Video Series – 6 Deliverables", "rate": 2400.0}], "parentInvoiceNumber": "MGL100007"}, {"invoiceNumber": "MGL100008-M1", "freelancerId": 19, "projectId": 312, "commissionerId": 32, "projectTitle": "Lagos Parks Mobile App Development", "milestoneDescription": "User authentication flow design", "milestoneNumber": 1, "issueDate": "2024-07-16", "dueDate": "2024-07-26", "totalAmount": 1200.0, "status": "paid", "milestones": [{"description": "User authentication flow design", "rate": 1200.0}], "parentInvoiceNumber": "MGL100008"}, {"invoiceNumber": "MGL100008-M2", "freelancerId": 19, "projectId": 312, "commissionerId": 32, "projectTitle": "Lagos Parks Mobile App Development", "milestoneDescription": "Park booking interface mockups", "milestoneNumber": 2, "issueDate": "2024-07-23", "dueDate": "2024-08-02", "totalAmount": 1350.0, "status": "paid", "milestones": [{"description": "Park booking interface mockups", "rate": 1350.0}], "parentInvoiceNumber": "MGL100008"}, {"invoiceNumber": "MGL100008-M3", "freelancerId": 19, "projectId": 312, "commissionerId": 32, "projectTitle": "Lagos Parks Mobile App Development", "milestoneDescription": "Maintenance reporting feature", "milestoneNumber": 3, "issueDate": "2024-07-30", "dueDate": "2024-08-09", "totalAmount": 1100.0, "status": "paid", "milestones": [{"description": "Maintenance reporting feature", "rate": 1100.0}], "parentInvoiceNumber": "MGL100008"}, {"invoiceNumber": "MGL100008-M4", "freelancerId": 19, "projectId": 312, "commissionerId": 32, "projectTitle": "Lagos Parks Mobile App Development", "milestoneDescription": "Navigation and menu structure", "milestoneNumber": 4, "issueDate": "2024-08-06", "dueDate": "2024-08-16", "totalAmount": 950.0, "status": "paid", "milestones": [{"description": "Navigation and menu structure", "rate": 950.0}], "parentInvoiceNumber": "MGL100008"}, {"invoiceNumber": "MGL100008-M5", "freelancerId": 19, "projectId": 312, "commissionerId": 32, "projectTitle": "Lagos Parks Mobile App Development", "milestoneDescription": "Push notification system design", "milestoneNumber": 5, "issueDate": "2024-08-13", "dueDate": "2024-08-23", "totalAmount": 1000.0, "status": "paid", "milestones": [{"description": "Push notification system design", "rate": 1000.0}], "parentInvoiceNumber": "MGL100008"}, {"invoiceNumber": "MGL100008-M6", "freelancerId": 19, "projectId": 312, "commissionerId": 32, "projectTitle": "Lagos Parks Mobile App Development", "milestoneDescription": "User profile and settings screens", "milestoneNumber": 6, "issueDate": "2024-08-20", "dueDate": "2024-08-30", "totalAmount": 800.0, "status": "paid", "milestones": [{"description": "User profile and settings screens", "rate": 800.0}], "parentInvoiceNumber": "MGL100008"}, {"invoiceNumber": "MGL100008-M7", "freelancerId": 19, "projectId": 312, "commissionerId": 32, "projectTitle": "Lagos Parks Mobile App Development", "milestoneDescription": "Payment integration interface", "milestoneNumber": 7, "issueDate": "2024-08-27", "dueDate": "2024-09-06", "totalAmount": 1400.0, "status": "paid", "milestones": [{"description": "Payment integration interface", "rate": 1400.0}], "parentInvoiceNumber": "MGL100008"}, {"invoiceNumber": "MGL100008-M8", "freelancerId": 19, "projectId": 312, "commissionerId": 32, "projectTitle": "Lagos Parks Mobile App Development", "milestoneDescription": "Final app prototype and handoff", "milestoneNumber": 8, "issueDate": "2024-09-03", "dueDate": "2024-09-13", "totalAmount": 1500.0, "status": "paid", "milestones": [{"description": "Final app prototype and handoff", "rate": 1500.0}], "parentInvoiceNumber": "MGL100008"}, {"invoiceNumber": "MGL000303-M1", "freelancerId": 31, "projectId": 303, "commissionerId": 34, "projectTitle": "Corlax iOS app UX", "milestoneDescription": "Milestone 1 for Corlax iOS app UX", "milestoneNumber": 1, "issueDate": "2025-07-21", "dueDate": "2025-08-04", "totalAmount": 2333.33, "status": "sent", "milestones": [{"description": "Login & onboarding flow design", "rate": 2333.33}], "isManualInvoice": true, "parentInvoiceNumber": "MGL000303"}, {"invoiceNumber": "MGL000301-M3", "freelancerId": 31, "projectId": 301, "commissionerId": 32, "projectTitle": "Lagos Parks Services website re-design", "milestoneDescription": "10 year anniversary graphic assets", "milestoneNumber": 3, "issueDate": "2025-07-21", "dueDate": "2025-08-04", "totalAmount": 1500.0, "status": "sent", "milestones": [{"description": "10 year anniversary graphic assets", "rate": 1500.0}], "isManualInvoice": true, "parentInvoiceNumber": "MGL000301"}, {"invoiceNumber": "MGL000314-M1", "freelancerId": 31, "projectId": 314, "commissionerId": 32, "projectTitle": "E-commerce Platform UI Redesign", "milestoneDescription": "User research and wireframing", "milestoneNumber": 1, "issueDate": "2025-07-21", "dueDate": "2025-08-04", "totalAmount": 3250.0, "status": "paid", "paidDate": "2025-07-21", "paymentDetails": {"paymentId": "pay_3250_314", "paymentMethod": "stripe", "platformFee": 162.5, "freelancerAmount": 3087.5, "currency": "USD", "processedAt": "2025-07-21T16:45:00Z"}, "milestones": [{"description": "User research and wireframing", "rate": 3250.0}], "isManualInvoice": true, "parentInvoiceNumber": "MGL000314"}]