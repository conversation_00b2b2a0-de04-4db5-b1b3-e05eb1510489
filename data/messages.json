[{"threadId": "31-32", "participants": [31, 32], "messages": [{"messageId": "msg-1001", "senderId": 32, "timestamp": "2025-06-11T09:32:00Z", "text": "Hi, next week we’ll start a new project. I’ll tell you all the details later", "read": {"31": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-1002", "senderId": 31, "timestamp": "2025-06-11T09:32:30Z", "text": "We can meet on Sunday BTW", "read": {"31": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-1003", "senderId": 32, "timestamp": "2025-06-11T09:33:10Z", "text": "Sunday works. Let’s aim for 3pm?", "read": {"31": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-1004", "senderId": 31, "timestamp": "2025-06-11T09:34:30Z", "text": "3pm is fine. What’s the scope of this new project tho?", "read": {"31": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-1005", "senderId": 32, "timestamp": "2025-06-11T09:38:40Z", "text": "Still finalizing it but it’ll involve UX audits + landing page redesign for the Parks client.", "read": {"31": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-1006", "senderId": 31, "timestamp": "2025-06-11T09:40:30Z", "text": "Okay but I need a better timeline this time. Last sprint was too chaotic.", "read": {"31": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-1007", "senderId": 32, "timestamp": "2025-06-11T09:41:40Z", "text": "Fair, but you submitted the wireframes 2 days late which bottlenecked dev. Let’s both plan tighter.", "read": {"31": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-1018", "senderId": 32, "timestamp": "2025-06-16T13:00:00Z", "text": "[Test] Unread message from contact 32.", "read": {"31": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-1019", "senderId": 32, "timestamp": "2025-07-07T10:30:00Z", "text": "Hey! Just wanted to check in on the project progress. How are the wireframes coming along?", "read": {"31": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-1020", "senderId": 32, "timestamp": "2025-07-07T11:15:00Z", "text": "Also, the client wants to schedule a review meeting for next week. Are you available Tuesday afternoon?", "read": {"31": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-1021", "senderId": 32, "timestamp": "2025-07-07T14:45:00Z", "text": "One more thing - they've updated the brand guidelines. I'll send you the new color palette shortly.", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T17:22:24.206Z", "text": "hey", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T17:22:35.033Z", "text": "hey baby", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 32, "timestamp": "2025-07-15T17:22:54.791Z", "text": "how are you?", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 32, "timestamp": "2025-07-15T17:31:11.699Z", "text": "hey", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:02:36.694Z", "text": "hey", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 32, "timestamp": "2025-07-15T19:03:04.282Z", "text": "how are you?", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 32, "timestamp": "2025-07-15T19:03:20.014Z", "text": "how are you?", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 32, "timestamp": "2025-07-15T19:03:35.665Z", "text": "ibwib<PERSON>", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:04:23.799Z", "text": "my oga", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:04:36.062Z", "text": "my oga", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:04:50.194Z", "text": "pasa", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:04:53.980Z", "text": "wewe", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 32, "timestamp": "2025-07-15T19:06:07.812Z", "text": "ewewe", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:06:32.306Z", "text": "hey yo", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:06:43.353Z", "text": "hey yo", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:07:10.355Z", "text": "my boss", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 32, "timestamp": "2025-07-15T19:16:58.048Z", "text": "hey kiddo", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 32, "timestamp": "2025-07-15T19:17:45.876Z", "text": "hey kiddo", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:26:57.384Z", "text": "dude", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:27:14.522Z", "text": "dude", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 32, "timestamp": "2025-07-15T19:28:01.121Z", "text": "tell me something wey i no fit do", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:28:26.084Z", "text": "tell me somethign wey i no fit do", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:28:51.582Z", "text": "tell me something wey i no fit do", "read": {"31": true, "32": true}, "isEncrypted": false}, {"senderId": 31, "timestamp": "2025-07-15T19:47:34.044Z", "text": "hey", "isEncrypted": false, "read": {"31": true, "32": true}}]}, {"threadId": "31-33", "participants": [31, 33], "messages": [{"messageId": "msg-1008", "senderId": 33, "timestamp": "2025-06-10T14:45:00Z", "text": "Mockups are ready. Let me know if we can align before EOD.", "read": {"31": true, "33": true}, "isEncrypted": false}, {"messageId": "msg-1009", "senderId": 31, "timestamp": "2025-06-10T15:00:00Z", "text": "Thanks. I'll check and revert shortly.", "read": {"31": true, "33": false}, "isEncrypted": false}, {"messageId": "msg-1010", "senderId": 33, "timestamp": "2025-06-10T16:22:00Z", "text": "Cool. Sending one more version with layout tweaks now.", "read": {"31": true, "33": true}, "isEncrypted": false}, {"messageId": "msg-1019", "senderId": 33, "timestamp": "2025-06-16T13:01:00Z", "text": "[Test] Unread message from contact 33.", "read": {"31": true, "33": true}, "isEncrypted": false}, {"messageId": "msg-1022", "senderId": 33, "timestamp": "2025-07-07T09:20:00Z", "text": "Morning! Quick question about the design system components - should the buttons follow the new brand colors?", "read": {"31": true, "33": true}, "isEncrypted": false}, {"messageId": "msg-1023", "senderId": 33, "timestamp": "2025-07-07T12:30:00Z", "text": "Also, I've finished the mobile mockups. Want to review them before I send to the client?", "read": {"31": true, "33": true}, "isEncrypted": false}]}, {"threadId": "31-34", "participants": [31, 34], "messages": [{"messageId": "msg-1011", "senderId": 34, "timestamp": "2025-06-09T12:00:00Z", "text": "Hey! Did you get my notes on the graphics revision?", "read": {"31": true, "34": true}, "isEncrypted": false}, {"messageId": "msg-1012", "senderId": 31, "timestamp": "2025-06-09T12:04:00Z", "text": "Yes! I’ve made updates and pushed them live. Let me know what you think.", "read": {"31": true, "34": false}, "isEncrypted": false}, {"messageId": "msg-1013", "senderId": 34, "timestamp": "2025-06-09T12:06:00Z", "text": "Typography still feels heavy. Try using the medium weight for headlines.", "read": {"31": true, "34": true}, "isEncrypted": false}, {"messageId": "msg-1020", "senderId": 34, "timestamp": "2025-06-16T13:02:00Z", "text": "[Test] Unread message from contact 34.", "read": {"31": true, "34": true}, "isEncrypted": false}, {"messageId": "msg-1024", "senderId": 34, "timestamp": "2025-07-07T16:00:00Z", "text": "Hi! The client loved the latest designs. They want to move forward with implementation. Can we schedule a kickoff call?", "read": {"31": true, "34": true}, "isEncrypted": false}]}, {"threadId": "31-35", "participants": [31, 35], "messages": [{"messageId": "msg-1014", "senderId": 35, "timestamp": "2025-06-13T10:00:00Z", "text": "Invoice sent. Let me know if payment clears today.", "read": {"31": true, "35": true}, "isEncrypted": false}, {"messageId": "msg-1015", "senderId": 31, "timestamp": "2025-06-13T10:10:00Z", "text": "Got it. Processing now.", "read": {"31": true, "35": false}, "isEncrypted": false}, {"messageId": "msg-1021", "senderId": 35, "timestamp": "2025-06-16T13:03:00Z", "text": "[Test] Unread message from contact 35.", "read": {"31": true, "35": true}, "isEncrypted": false}, {"messageId": "msg-1025", "senderId": 35, "timestamp": "2025-07-07T17:30:00Z", "text": "Payment processed successfully! Thanks for the quick turnaround on the invoice. Looking forward to the next project.", "read": {"31": false, "35": true}, "isEncrypted": false}]}, {"threadId": "31-36", "participants": [31, 36], "messages": [{"messageId": "msg-1016", "senderId": 36, "timestamp": "2025-06-13T11:00:00Z", "text": "Can we move our meeting to 4PM?", "read": {"31": true, "36": true}, "isEncrypted": false}, {"messageId": "msg-1017", "senderId": 31, "timestamp": "2025-06-13T11:03:00Z", "text": "Sure. Blocking it out now.", "read": {"31": true, "36": false}, "isEncrypted": false}, {"messageId": "msg-1022", "senderId": 36, "timestamp": "2025-06-16T13:04:00Z", "text": "[Test] Unread message from contact 36.", "read": {"31": true, "36": true}, "isEncrypted": false}, {"messageId": "msg-1026", "senderId": 36, "timestamp": "2025-07-07T18:45:00Z", "text": "Hey! Just confirming our meeting is still on for tomorrow at 4PM. I have some exciting updates to share about the project scope.", "read": {"31": false, "36": true}, "isEncrypted": false}]}, {"threadId": "28-32", "participants": [28, 32], "messages": [{"messageId": "msg-2001", "senderId": 28, "timestamp": "2025-07-13T14:20:00Z", "text": "Hi <PERSON><PERSON>! Just finished the motion graphics for the Lagos Parks project. Ready for review!", "read": {"28": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-2002", "senderId": 32, "timestamp": "2025-07-13T14:45:00Z", "text": "Great work Billy! I'll review it this afternoon and get back to you.", "read": {"28": false, "32": true}, "isEncrypted": false}]}, {"threadId": "29-32", "participants": [29, 32], "messages": [{"messageId": "msg-3001", "senderId": 29, "timestamp": "2025-07-14T10:15:00Z", "text": "Good morning! I have some questions about the mobile app wireframes. Can we schedule a quick call?", "read": {"29": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-3002", "senderId": 32, "timestamp": "2025-07-14T10:30:00Z", "text": "Sure! How about 2pm today? I'm free for about 30 minutes.", "read": {"29": false, "32": true}, "isEncrypted": false}, {"messageId": "msg-3003", "senderId": 29, "timestamp": "2025-07-14T10:32:00Z", "text": "Perfect! I'll send you the meeting link shortly.", "read": {"29": true, "32": true}, "isEncrypted": false}]}, {"threadId": "27-32", "participants": [27, 32], "messages": [{"messageId": "msg-4001", "senderId": 27, "timestamp": "2025-07-12T16:30:00Z", "text": "Hi! I saw your post about the new project opportunities. I'd love to discuss potential collaboration.", "read": {"27": true, "32": true}, "isEncrypted": false}, {"messageId": "msg-4002", "senderId": 32, "timestamp": "2025-07-12T17:15:00Z", "text": "Hi <PERSON>! Thanks for reaching out. Let's set up a time to chat about your background and what you're looking for.", "read": {"27": true, "32": true}, "isEncrypted": false}]}, {"threadId": "26-32", "participants": [26, 32], "messages": [{"messageId": "msg-5001", "senderId": 26, "timestamp": "2025-07-14T08:45:00Z", "text": "Morning! The UI components are ready for the design system. Should I proceed with the documentation?", "read": {"26": true, "32": false}, "isEncrypted": false}]}, {"threadId": "1-32", "participants": [1, 32], "messages": []}, {"threadId": "3-32", "participants": [3, 32], "messages": []}, {"threadId": "5-32", "participants": [32, 5], "messages": [], "metadata": {"createdAt": "2025-07-16T14:24:32.319Z", "initiatedBy": 32, "status": "pending_response"}}, {"threadId": "8-32", "participants": [32, 8], "messages": [], "metadata": {"createdAt": "2025-07-16T14:24:45.634Z", "initiatedBy": 32, "status": "pending_response"}}]