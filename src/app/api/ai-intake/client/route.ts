// src/app/api/ai-intake/client/route.ts
import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function POST(req: Request) {
  try {
    const { prompt, budget, step, selectedFreelancerId, editedRequirements, confirmed } = await req.json();

    if (!prompt) {
      return NextResponse.json({ error: 'Missing prompt' }, { status: 400 });
    }

    // Read data files
    const freelancersPath = path.join(process.cwd(), 'data', 'freelancers.json');
    const organizationsPath = path.join(process.cwd(), 'data', 'organizations.json');
    const gigsPath = path.join(process.cwd(), 'data', 'gigs.json');
    const categoriesPath = path.join(process.cwd(), 'data', 'gigs', 'gig-categories.json');
    const toolsPath = path.join(process.cwd(), 'data', 'gigs', 'gig-tools.json');

    const freelancers = JSON.parse(fs.readFileSync(freelancersPath, 'utf-8'));
    const organizations = JSON.parse(fs.readFileSync(organizationsPath, 'utf-8'));
    const gigs = JSON.parse(fs.readFileSync(gigsPath, 'utf-8'));
    const categories = JSON.parse(fs.readFileSync(categoriesPath, 'utf-8'));
    const tools = JSON.parse(fs.readFileSync(toolsPath, 'utf-8'));

    // Step 1: Initial prompt - ask for budget
    if (!budget || step === 'initial') {
      // Extract keywords from prompt for matching
      const promptLower = prompt.toLowerCase();

      // Find matching categories (flatten subcategories)
      const allCategories: any[] = [];
      categories.forEach((cat: any) => {
        if (cat.subcategories) {
          cat.subcategories.forEach((sub: any) => {
            allCategories.push({ ...sub, parentId: cat.id, parentLabel: cat.label });
          });
        }
      });

      const matchingCategories = allCategories.filter((cat: any) =>
        promptLower.includes(cat.name.toLowerCase()) ||
        promptLower.includes(cat.description.toLowerCase()) ||
        cat.keywords?.some((keyword: string) => promptLower.includes(keyword.toLowerCase()))
      );

      const response = {
        step: 'budget_request',
        message: `Great! I understand you want to build: "${prompt}". What's your budget range for this project?`,
        categories: matchingCategories.map(c => c.name).slice(0, 5),
        nextStep: 'budget'
      };

      return NextResponse.json({ result: JSON.stringify(response, null, 2) });
    }

    // Step 2: Budget provided - show matching freelancers with "Post as Gig" option
    if (!selectedFreelancerId && !confirmed) {
      const promptLower = prompt.toLowerCase();

      // Find matching categories (flatten subcategories)
      const allCategories: any[] = [];
      categories.forEach((cat: any) => {
        if (cat.subcategories) {
          cat.subcategories.forEach((sub: any) => {
            allCategories.push({ ...sub, parentId: cat.id, parentLabel: cat.label });
          });
        }
      });

      const matchingCategories = allCategories.filter((cat: any) =>
        promptLower.includes(cat.name.toLowerCase()) ||
        promptLower.includes(cat.description.toLowerCase()) ||
        cat.keywords?.some((keyword: string) => promptLower.includes(keyword.toLowerCase()))
      );

      // Find matching tools
      const matchingTools = tools.filter((tool: any) =>
        promptLower.includes(tool.name.toLowerCase()) ||
        tool.keywords?.some((keyword: string) => promptLower.includes(keyword.toLowerCase()))
      );

      // Find matching freelancers based on skills and budget
      const budgetNum = parseInt(budget.replace(/[^0-9]/g, '')) || 5000;
      const matchingFreelancers = freelancers.filter((freelancer: any) => {
        const skills = freelancer.skills || [];
        const hourlyRate = freelancer.hourlyRate || 50;

        // Check if freelancer's rate fits budget (assuming 40 hours for project)
        const estimatedCost = hourlyRate * 40;
        const withinBudget = estimatedCost <= budgetNum * 1.2; // 20% buffer

        const skillMatch = skills.some((skill: string) =>
          promptLower.includes(skill.toLowerCase()) ||
          matchingCategories.some((cat: any) => cat.name.toLowerCase().includes(skill.toLowerCase())) ||
          matchingTools.some((tool: any) => tool.name.toLowerCase().includes(skill.toLowerCase()))
        );

        return skillMatch && withinBudget;
      }).slice(0, 8); // Show more matches with budget

      const response = {
        step: 'freelancer_results',
        message: `Perfect! Based on your budget of ${budget}, here are the best freelancers for "${prompt}":`,
        freelancers: matchingFreelancers.map((f: any) => ({
          id: f.id,
          name: f.name,
          title: f.title,
          skills: f.skills?.slice(0, 4) || [],
          hourlyRate: f.hourlyRate,
          rating: f.rating,
          avatar: f.avatar,
          completedProjects: f.completedProjects || 0,
          estimatedCost: (f.hourlyRate || 50) * 40
        })),
        categories: matchingCategories.map((c: any) => c.name).slice(0, 5),
        tools: matchingTools.map((t: any) => t.name).slice(0, 5),
        showPostGigButton: true,
        projectDetails: {
          description: prompt,
          budget: budget,
          categories: matchingCategories.map((c: any) => c.name).slice(0, 3)
        }
      };

      return NextResponse.json({ result: JSON.stringify(response, null, 2) });
    }

    // Step 3: Generate project requirements for confirmation
    if (!confirmed && !editedRequirements) {
      const promptLower = prompt.toLowerCase();
      const budgetNum = parseInt(budget.replace(/[^0-9]/g, '')) || 5000;

      // Find matching categories and tools for requirements
      const allCategories: any[] = [];
      categories.forEach((cat: any) => {
        if (cat.subcategories) {
          cat.subcategories.forEach((sub: any) => {
            allCategories.push({ ...sub, parentId: cat.id, parentLabel: cat.label });
          });
        }
      });

      const matchingCategories = allCategories.filter((cat: any) =>
        promptLower.includes(cat.name.toLowerCase()) ||
        promptLower.includes(cat.description.toLowerCase()) ||
        cat.keywords?.some((keyword: string) => promptLower.includes(keyword.toLowerCase()))
      );

      const matchingTools = tools.filter((tool: any) =>
        promptLower.includes(tool.name.toLowerCase()) ||
        tool.keywords?.some((keyword: string) => promptLower.includes(keyword.toLowerCase()))
      );

      // Generate project requirements based on AI analysis
      const projectRequirements = {
        title: `${matchingCategories[0]?.name || 'Custom'} Project - ${prompt.split(' ').slice(0, 4).join(' ')}`,
        category: matchingCategories[0]?.parentLabel || 'General',
        subcategory: matchingCategories[0]?.name || 'Custom',
        description: prompt,
        lowerBudget: Math.floor(budgetNum * 0.8),
        upperBudget: budgetNum,
        estimatedHours: Math.ceil(budgetNum / 75), // Assuming $75/hour average
        deliveryTimeWeeks: Math.ceil(budgetNum / 75 / 40) || 1, // Assuming 40 hours/week
        startType: 'Immediately',
        executionMethod: budgetNum > 3000 ? 'milestone' : 'completion',
        toolsRequired: matchingTools.slice(0, 3).map((t: any) => t.name),
        skills: matchingCategories.slice(0, 3).map((c: any) => c.name),
        milestones: budgetNum > 3000 ? [
          {
            title: 'Project Setup & Planning',
            description: 'Initial project setup, requirements gathering, and planning phase',
            percentage: 25
          },
          {
            title: 'Development & Implementation',
            description: 'Core development and implementation of project requirements',
            percentage: 50
          },
          {
            title: 'Testing & Refinement',
            description: 'Testing, bug fixes, and refinements based on feedback',
            percentage: 25
          }
        ] : []
      };

      const response = {
        step: 'requirements_confirmation',
        message: `Based on your project "${prompt}" with a budget of ${budget}, I've generated these project requirements:`,
        projectRequirements: projectRequirements,
        selectedFreelancerId: selectedFreelancerId,
        isPrivateGig: !!selectedFreelancerId,
        confirmationNeeded: true
      };

      return NextResponse.json({ result: JSON.stringify(response, null, 2) });
    }

    // Step 4: Handle confirmation or create gig
    if (confirmed || editedRequirements) {
      const finalRequirements = editedRequirements || JSON.parse(budget); // budget contains requirements if editing

      // Create new gig entry
      const newGig = {
        id: Date.now(), // Simple ID generation
        title: finalRequirements.title,
        organizationId: 1, // Default organization - should be from session
        commissionerId: 32, // Should be from session
        category: finalRequirements.category,
        subcategory: finalRequirements.subcategory,
        tags: finalRequirements.skills || [],
        hourlyRateMin: Math.floor(finalRequirements.lowerBudget / finalRequirements.estimatedHours),
        hourlyRateMax: Math.ceil(finalRequirements.upperBudget / finalRequirements.estimatedHours),
        description: finalRequirements.description,
        deliveryTimeWeeks: finalRequirements.deliveryTimeWeeks,
        estimatedHours: finalRequirements.estimatedHours,
        status: selectedFreelancerId ? 'Private' : 'Available',
        toolsRequired: finalRequirements.toolsRequired || [],
        executionMethod: finalRequirements.executionMethod,
        milestones: finalRequirements.milestones?.map((m: any, index: number) => ({
          id: `${Date.now()}-${index}`,
          title: m.title,
          description: m.description,
          startDate: null,
          endDate: null
        })) || [],
        startType: finalRequirements.startType,
        endDate: new Date(Date.now() + finalRequirements.deliveryTimeWeeks * 7 * 24 * 60 * 60 * 1000).toISOString(),
        lowerBudget: finalRequirements.lowerBudget,
        upperBudget: finalRequirements.upperBudget,
        postedDate: new Date().toISOString().split('T')[0],
        notes: `Budget range: $${finalRequirements.lowerBudget.toLocaleString()} - $${finalRequirements.upperBudget.toLocaleString()}`,
        targetFreelancerId: selectedFreelancerId || null
      };

      // In a real implementation, this would write to the database
      // For now, we'll return the gig data for the frontend to handle
      const response = {
        step: 'gig_created',
        message: selectedFreelancerId
          ? `Private gig request sent successfully! The freelancer will see this in their gig requests.`
          : `Gig posted successfully! Freelancers can now see and apply to your project.`,
        gigData: newGig,
        redirectTo: selectedFreelancerId
          ? '/commissioner-dashboard/projects-and-invoices'
          : '/commissioner-dashboard/job-listings',
        success: true
      };

      return NextResponse.json({ result: JSON.stringify(response, null, 2) });
    }
  } catch (err) {
    console.error('[AI_CLIENT_PROMPT_ERROR]', err);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}