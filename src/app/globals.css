@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;700&display=swap");
@import "tailwindcss";

/* -------------------------
   Base Tailwind Reset Layer
-------------------------- */
@layer base {
  body {
    @apply leading-[normal] m-0;
  }

  *::before,
  *::after {
    border-width: 0;
  }
}

/* -------------------------
   Utility Classes
-------------------------- */
@layer utilities {
  /* Hide scrollbar for horizontal scrolling */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
}

/* -------------------------
   Keyframe animations
-------------------------- */
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-down {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Smooth page transitions */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px) scale(0.98);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: opacity 400ms ease-out, transform 400ms ease-out;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px) scale(0.98);
  transition: opacity 300ms ease-in, transform 300ms ease-in;
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Smooth hover transitions */
.smooth-hover {
  transition: all 0.2s ease-out;
}

.smooth-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Animation utilities */
.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

/* -------------------------------------------------------
   DayPicker Theme — ARTISH Pink, Clean, Stable, Accessible
-------------------------------------------------------- */

/* Color variables */
:root,
.rdp-root {
  --rdp-accent-color: #eb1966 !important;
  --rdp-background-color: #FCD5E3 !important;
  --rdp-accent-color-dark: #eb1966 !important;
  --rdp-background-color-dark: #FCD5E3 !important;
  --rdp-outline: none !important;
  --rdp-outline-selected: none !important;
}

/* Core button layout: all day buttons uniform */
.rdp-button {
  width: 36px !important;
  height: 36px !important;
  padding: 0 !important;
  margin: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  line-height: 1 !important;
  background: transparent !important;
  border: none !important;
  border-radius: 9999px !important;
  box-shadow: none !important;
  box-sizing: border-box !important;
  outline: none !important;
  transition: all 0.2s ease-in-out;
}

/* Clean up day wrappers */
.rdp-day,
.rdp-cell,
td[role='gridcell'] {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* ✅ Selected day */
[aria-selected='true'] {
  background-color: #FCD5E3 !important;
  color: #eb1966 !important;
  font-weight: 600 !important;
  border: 2px solid #eb1966 !important;
  border-radius: 9999px !important;
  box-shadow: none !important;
  outline: none !important;
}

/* ✅ Hovered selected day */
[aria-selected='true']:hover {
  background-color: #eb1966 !important;
  color: #fff !important;
}

/* ✅ Today (unselected) */
.rdp-day_today:not([aria-selected='true']) > button {
  border: 2px solid #eb1966 !important;
  color: #eb1966 !important;
  border-radius: 9999px !important;
}

/* ✅ Focus ring for keyboard users */
.rdp-button:focus-visible {
  outline: 2px solid #eb1966 !important;
  outline-offset: 2px;
  border-radius: 9999px;
}

/* ✅ Disabled past dates — greyed out + unclickable */
.rdp-day_disabled:hover {
  background: transparent !important;
  color: #d1d5db !important;
}

/* ✅ Nav arrows — always black */
.rdp-nav_button svg {
  fill: #000 !important;
  stroke: #000 !important;
  color: #000 !important;
  opacity: 1 !important;
}
.rdp-nav_button {
  background: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}
.rdp-day_button[disabled] {
  color: #d1d5db !important;             /* grey */
  background: transparent !important;
  border: none !important;
  cursor: not-allowed !important;
  opacity: 0.4 !important;
  pointer-events: none !important;
}